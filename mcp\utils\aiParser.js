const OpenAI = require('openai');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const parseQuery = async (query, language = 'en') => {
  try {
    const systemPrompt = language === 'ur' 
      ? `آپ ایک فارم مینجمنٹ AI ہیں۔ صارف کے سوال کو parse کریں اور JSON میں جواب دیں:
{
  "entity": "animals|plants|zones|tasks|inventory|machinery|requests",
  "intent": "list|create|update|delete|count",
  "filters": {},
  "subcollection": null
}

مثال: "باغ 2 میں حاملہ بکریاں دکھائیں"
جواب: {"entity": "animals", "intent": "list", "filters": {"type": "goat", "status": "pregnant", "zone": "باغ 2"}}`
      : `You are a farm management AI. Parse user queries into structured JSON:
{
  "entity": "animals|plants|zones|tasks|inventory|machinery|requests",
  "intent": "list|create|update|delete|count", 
  "filters": {},
  "subcollection": null
}

Example: "Show pregnant goats in Garden 2"
Response: {"entity": "animals", "intent": "list", "filters": {"type": "goat", "status": "pregnant", "zone": "Garden 2"}}`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: query }
      ],
      temperature: 0.1,
      max_tokens: 500
    });

    const content = response.choices[0].message.content.trim();
    
    // Extract JSON from response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in AI response');
    }

    const parsed = JSON.parse(jsonMatch[0]);
    
    // Validate required fields
    if (!parsed.entity || !parsed.intent) {
      throw new Error('Missing required fields in parsed query');
    }

    return {
      entity: parsed.entity,
      intent: parsed.intent,
      filters: parsed.filters || {},
      subcollection: parsed.subcollection || null
    };

  } catch (error) {
    console.error('AI Parser Error:', error);
    
    // Fallback parsing for common patterns
    const fallback = fallbackParser(query, language);
    if (fallback) {
      return fallback;
    }
    
    throw new Error(`Failed to parse query: ${error.message}`);
  }
};

const fallbackParser = (query, language) => {
  const lowerQuery = query.toLowerCase();
  
  // Common entities mapping
  const entityMap = language === 'ur' ? {
    'جانور': 'animals', 'بکری': 'animals', 'گائے': 'animals',
    'پودے': 'plants', 'فصل': 'plants',
    'علاقہ': 'zones', 'باغ': 'zones',
    'کام': 'tasks', 'ٹاسک': 'tasks'
  } : {
    'animal': 'animals', 'goat': 'animals', 'cow': 'animals',
    'plant': 'plants', 'crop': 'plants',
    'zone': 'zones', 'garden': 'zones',
    'task': 'tasks', 'work': 'tasks'
  };

  for (const [keyword, entity] of Object.entries(entityMap)) {
    if (lowerQuery.includes(keyword)) {
      return {
        entity,
        intent: 'list',
        filters: {},
        subcollection: null
      };
    }
  }

  return null;
};

module.exports = { parseQuery };