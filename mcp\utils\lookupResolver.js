const { db } = require('../firebase');

const resolveFilters = async (filters, farmId, language = 'en') => {
  try {
    const resolvedFilters = { ...filters };
    
    // Fields that need lookup resolution
    const lookupFields = ['zone', 'type', 'breed', 'category', 'status'];
    
    for (const [field, value] of Object.entries(filters)) {
      if (lookupFields.includes(field) && typeof value === 'string') {
        const resolvedId = await resolveLookupValue(value, field, farmId, language);
        if (resolvedId) {
          resolvedFilters[field] = resolvedId;
        }
      }
    }
    
    return resolvedFilters;
  } catch (error) {
    console.error('Lookup resolution error:', error);
    return filters; // Return original filters if resolution fails
  }
};

const resolveLookupValue = async (displayValue, fieldType, farmId, language) => {
  try {
    // First check farm-specific lookups
    const farmLookupsRef = db.collection(`farms/${farmId}/lookups`);
    const farmSnapshot = await farmLookupsRef
      .where('type', '==', fieldType)
      .where(language === 'ur' ? 'label_ur' : 'label_en', '==', displayValue)
      .limit(1)
      .get();
    
    if (!farmSnapshot.empty) {
      return farmSnapshot.docs[0].id;
    }
    
    // Then check global lookups
    const globalLookupsRef = db.collection('lookups');
    const globalSnapshot = await globalLookupsRef
      .where('type', '==', fieldType)
      .where(language === 'ur' ? 'label_ur' : 'label_en', '==', displayValue)
      .limit(1)
      .get();
    
    if (!globalSnapshot.empty) {
      return globalSnapshot.docs[0].id;
    }
    
    // Check zones collection directly
    if (fieldType === 'zone') {
      const zonesRef = db.collection(`farms/${farmId}/zones`);
      const zoneSnapshot = await zonesRef
        .where(language === 'ur' ? 'name_ur' : 'name_en', '==', displayValue)
        .limit(1)
        .get();
      
      if (!zoneSnapshot.empty) {
        return zoneSnapshot.docs[0].id;
      }
    }
    
    // If no exact match found, return original value
    return displayValue;
    
  } catch (error) {
    console.error(`Error resolving lookup for ${fieldType}:`, error);
    return displayValue;
  }
};

const getLookupLabel = async (id, fieldType, farmId, language = 'en') => {
  try {
    // Check farm-specific lookups first
    const farmDoc = await db.doc(`farms/${farmId}/lookups/${id}`).get();
    if (farmDoc.exists) {
      const data = farmDoc.data();
      return language === 'ur' ? data.label_ur : data.label_en;
    }
    
    // Check global lookups
    const globalDoc = await db.doc(`lookups/${id}`).get();
    if (globalDoc.exists) {
      const data = globalDoc.data();
      return language === 'ur' ? data.label_ur : data.label_en;
    }
    
    // Check zones if fieldType is zone
    if (fieldType === 'zone') {
      const zoneDoc = await db.doc(`farms/${farmId}/zones/${id}`).get();
      if (zoneDoc.exists) {
        const data = zoneDoc.data();
        return language === 'ur' ? data.name_ur : data.name_en;
      }
    }
    
    return id; // Return ID if no label found
    
  } catch (error) {
    console.error(`Error getting lookup label for ${id}:`, error);
    return id;
  }
};

module.exports = { resolveFilters, getLookupLabel };