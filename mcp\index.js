const { onRequest } = require('firebase-functions/v2/https');
const express = require('express');
const cors = require('cors');
const queryRoute = require('./routes/queryRoute');

const app = express();

app.use(cors({ origin: true }));
app.use(express.json({ limit: '10mb' }));
app.use('/query', queryRoute);

app.get('/', (req, res) => {
  res.json({ status: 'MCP Server running', version: '1.0.0' });
});

exports.mcpServer = onRequest({
  region: 'us-central1',
  memory: '1GiB',
  timeoutSeconds: 300
}, app);