const OpenAI = require('openai');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const analyzeImage = async (imageBase64, language = 'en') => {
  try {
    // Remove data URL prefix if present
    const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
    
    const systemPrompt = language === 'ur'
      ? `آپ ایک زرعی ماہر ہیں۔ تصویر دیکھ کر JSON میں جواب دیں:
{
  "diagnosis": "تشخیص کی تفصیل",
  "recommendations": ["تجویز 1", "تجویز 2"],
  "confidence": 0.85
}

پودوں، جانوروں، یا فصلوں کی صحت کا جائزہ لیں۔`
      : `You are an agricultural expert. Analyze the image and respond in JSON:
{
  "diagnosis": "Detailed diagnosis",
  "recommendations": ["Recommendation 1", "Recommendation 2"], 
  "confidence": 0.85
}

Assess plant, animal, or crop health conditions.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4-vision-preview',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: language === 'ur' 
                ? 'اس تصویر کا تجزیہ کریں اور صحت کی تشخیص فراہم کریں'
                : 'Analyze this image and provide health diagnosis'
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${base64Data}`,
                detail: 'high'
              }
            }
          ]
        }
      ],
      max_tokens: 1000,
      temperature: 0.2
    });

    const content = response.choices[0].message.content.trim();
    
    // Extract JSON from response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      // Fallback response
      return {
        diagnosis: language === 'ur' 
          ? 'تصویر کا تجزیہ مکمل ہوا لیکن مخصوص تشخیص دستیاب نہیں'
          : 'Image analyzed but specific diagnosis not available',
        recommendations: [
          language === 'ur' 
            ? 'مزید واضح تصویر فراہم کریں'
            : 'Provide a clearer image'
        ],
        confidence: 0.5
      };
    }

    const parsed = JSON.parse(jsonMatch[0]);
    
    return {
      diagnosis: parsed.diagnosis || (language === 'ur' ? 'تشخیص دستیاب نہیں' : 'No diagnosis available'),
      recommendations: parsed.recommendations || [],
      confidence: parsed.confidence || 0.5
    };

  } catch (error) {
    console.error('Image Analysis Error:', error);
    
    return {
      diagnosis: language === 'ur' 
        ? 'تصویر کے تجزیے میں خرابی'
        : 'Error analyzing image',
      recommendations: [
        language === 'ur' 
          ? 'دوبارہ کوشش کریں'
          : 'Please try again'
      ],
      confidence: 0.0
    };
  }
};

module.exports = { analyzeImage };