const { db } = require('../firebase');

const getDocuments = async (collectionPath, filters = {}, subcollection = null) => {
  try {
    let query = db.collection(collectionPath);

    // Apply filters
    Object.entries(filters).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          query = query.where(field, 'in', value);
        } else {
          query = query.where(field, '==', value);
        }
      }
    });

    const snapshot = await query.get();
    const documents = [];

    for (const doc of snapshot.docs) {
      const data = { id: doc.id, ...doc.data() };
      
      // If subcollection requested, fetch it
      if (subcollection) {
        const subSnapshot = await doc.ref.collection(subcollection).get();
        data[subcollection] = subSnapshot.docs.map(subDoc => ({
          id: subDoc.id,
          ...subDoc.data()
        }));
      }
      
      documents.push(data);
    }

    return documents;
  } catch (error) {
    console.error('Firestore query error:', error);
    throw new Error('Failed to fetch documents from Firestore');
  }
};

const getDocument = async (documentPath) => {
  try {
    const doc = await db.doc(documentPath).get();
    if (!doc.exists) {
      return null;
    }
    return { id: doc.id, ...doc.data() };
  } catch (error) {
    console.error('Firestore get document error:', error);
    throw new Error('Failed to fetch document from Firestore');
  }
};

module.exports = { getDocuments, getDocument };