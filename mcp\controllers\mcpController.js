const aiParser = require('../utils/aiParser');
const imageAnalyzer = require('../utils/imageAnalyzer');
const firestoreModel = require('../models/firestoreModel');
const lookupResolver = require('../utils/lookupResolver');

const handleQuery = async (req, res) => {
  try {
    const { inputType, query, imageBase64, farmId, language = 'en' } = req.body;

    if (!farmId) {
      return res.status(400).json({ 
        error: language === 'ur' ? 'فارم آئی ڈی درکار ہے' : 'Farm ID is required' 
      });
    }

    if (inputType === 'text') {
      if (!query) {
        return res.status(400).json({ 
          error: language === 'ur' ? 'سوال درکار ہے' : 'Query is required' 
        });
      }

      // Parse natural language query
      const parsedQuery = await aiParser.parseQuery(query, language);
      console.log('Parsed query:', parsedQuery);

      // Resolve lookup values to IDs
      const resolvedFilters = await lookupResolver.resolveFilters(
        parsedQuery.filters, 
        farmId, 
        language
      );

      // Build collection path
      const collectionPath = `farms/${farmId}/${parsedQuery.entity}`;

      // Query Firestore
      const results = await firestoreModel.getDocuments(
        collectionPath, 
        resolvedFilters,
        parsedQuery.subcollection
      );

      return res.json({
        success: true,
        data: results,
        entity: parsedQuery.entity,
        intent: parsedQuery.intent,
        message: language === 'ur' 
          ? `${results.length} نتائج ملے` 
          : `Found ${results.length} results`
      });

    } else if (inputType === 'image') {
      if (!imageBase64) {
        return res.status(400).json({ 
          error: language === 'ur' ? 'تصویر درکار ہے' : 'Image is required' 
        });
      }

      // Analyze image
      const diagnosis = await imageAnalyzer.analyzeImage(imageBase64, language);

      return res.json({
        success: true,
        diagnosis: diagnosis.diagnosis,
        recommendations: diagnosis.recommendations,
        confidence: diagnosis.confidence
      });

    } else {
      return res.status(400).json({ 
        error: language === 'ur' 
          ? 'غلط ان پٹ ٹائپ' 
          : 'Invalid input type. Use "text" or "image"' 
      });
    }

  } catch (error) {
    console.error('MCP Controller Error:', error);
    const language = req.body.language || 'en';
    
    return res.status(500).json({
      success: false,
      error: language === 'ur' 
        ? 'سرور میں خرابی' 
        : 'Internal server error',
      details: error.message
    });
  }
};

module.exports = { handleQuery };